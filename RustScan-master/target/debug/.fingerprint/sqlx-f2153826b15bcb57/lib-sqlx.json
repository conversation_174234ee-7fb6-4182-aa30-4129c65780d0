{"rustc": 16542739221701554903, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"migrate\", \"mysql\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"tls-native-tls\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 5347358027863023418, "path": 1126100997141132055, "deps": [[996810380461694889, "sqlx_core", false, 885082504128885467], [11838249260056359578, "sqlx_sqlite", false, 5789418889087059354], [15634168271133386882, "sqlx_postgres", false, 7883214241064899205], [15948984357385107951, "sqlx_mysql", false, 6978754413616385883]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-f2153826b15bcb57/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}