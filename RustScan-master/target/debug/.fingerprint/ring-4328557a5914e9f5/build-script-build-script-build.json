{"rustc": 16542739221701554903, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"std\", \"wasm32_unknown_unknown_js\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 10923283750729064489, "deps": [[7719488414895976765, "cc", false, 11091937574946897144]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ring-4328557a5914e9f5/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}