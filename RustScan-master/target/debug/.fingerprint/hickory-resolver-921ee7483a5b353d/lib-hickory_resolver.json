{"rustc": 16542739221701554903, "features": "[\"default\", \"dns-over-rustls\", \"dns-over-tls\", \"ipconfig\", \"resolv-conf\", \"rustls\", \"system-config\", \"tokio\", \"tokio-runtime\", \"tokio-rustls\"]", "declared_features": "[\"backtrace\", \"default\", \"dns-over-h3\", \"dns-over-https\", \"dns-over-https-rustls\", \"dns-over-native-tls\", \"dns-over-openssl\", \"dns-over-quic\", \"dns-over-rustls\", \"dns-over-tls\", \"dnssec\", \"dnssec-openssl\", \"dnssec-ring\", \"ipconfig\", \"native-certs\", \"resolv-conf\", \"rustls\", \"serde\", \"serde-config\", \"system-config\", \"testing\", \"tokio\", \"tokio-native-tls\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-rustls\", \"webpki-roots\"]", "target": 8977667725728285596, "profile": 5546367963559690712, "path": 16376226333107653366, "deps": [[2062481783838671931, "parking_lot", false, 8236097983047555158], [3722963349756955755, "once_cell", false, 13593566721817833526], [5134553717702182819, "hickory_proto", false, 12090875720352522442], [5138218615291878843, "tokio", false, 3678924750994709159], [5307886053132500999, "resolv_conf", false, 2667307698004146365], [6197030920712631283, "lru_cache", false, 11826716595442473071], [8008191657135824715, "thiserror", false, 18229956334786815544], [8606274917505247608, "tracing", false, 2421653547181976407], [9618267541840914482, "smallvec", false, 14029220784463473733], [10411997081178400487, "cfg_if", false, 4021780138153559489], [10629569228670356391, "futures_util", false, 7484272118170343626], [11295624341523567602, "rustls", false, 9285723978765589070], [13208667028893622512, "rand", false, 13984995954636255743], [16622232390123975175, "tokio_rustls", false, 9079157981423831350]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hickory-resolver-921ee7483a5b353d/dep-lib-hickory_resolver", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}