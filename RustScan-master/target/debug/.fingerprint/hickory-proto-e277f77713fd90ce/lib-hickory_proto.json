{"rustc": 16542739221701554903, "features": "[\"dns-over-rustls\", \"dns-over-tls\", \"rustls\", \"rustls-pemfile\", \"tokio\", \"tokio-runtime\", \"tokio-rustls\"]", "declared_features": "[\"backtrace\", \"bytes\", \"default\", \"dns-over-h3\", \"dns-over-https\", \"dns-over-https-rustls\", \"dns-over-native-tls\", \"dns-over-openssl\", \"dns-over-quic\", \"dns-over-rustls\", \"dns-over-tls\", \"dnssec\", \"dnssec-openssl\", \"dnssec-ring\", \"h2\", \"h3\", \"h3-quinn\", \"http\", \"js-sys\", \"mdns\", \"native-certs\", \"native-tls\", \"openssl\", \"quinn\", \"ring\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde-config\", \"socket2\", \"testing\", \"text-parsing\", \"tokio\", \"tokio-native-tls\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-rustls\", \"wasm-bindgen\", \"wasm-bindgen-crate\", \"webpki-roots\"]", "target": 845874235032908426, "profile": 5546367963559690712, "path": 18066451850638148740, "deps": [[5103565458935487, "futures_io", false, 5977468153962090210], [95042085696191081, "ipnet", false, 127614727811876354], [1282593816529001625, "data_encoding", false, 1556224151073374587], [1811549171721445101, "futures_channel", false, 15072754643250828559], [3150220818285335163, "url", false, 6750346232297437404], [3722963349756955755, "once_cell", false, 13593566721817833526], [5138218615291878843, "tokio", false, 3678924750994709159], [6376232718484714452, "idna", false, 16936315099152724759], [8008191657135824715, "thiserror", false, 18229956334786815544], [8606274917505247608, "tracing", false, 2421653547181976407], [9939919806631075475, "tinyvec", false, 3997273944114021872], [10399806393418013851, "enum_as_inner", false, 16512940207534853954], [10411997081178400487, "cfg_if", false, 4021780138153559489], [10629569228670356391, "futures_util", false, 7484272118170343626], [11295624341523567602, "rustls", false, 9285723978765589070], [13208667028893622512, "rand", false, 13984995954636255743], [13787082801403653804, "async_trait", false, 11681929943801932685], [16311359161338405624, "rustls_pemfile", false, 14565655473645463312], [16622232390123975175, "tokio_rustls", false, 9079157981423831350]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hickory-proto-e277f77713fd90ce/dep-lib-hickory_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}