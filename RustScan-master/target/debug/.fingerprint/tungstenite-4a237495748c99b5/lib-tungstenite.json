{"rustc": 16542739221701554903, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 5347358027863023418, "path": 1418375972900389695, "deps": [[1282593816529001625, "data_encoding", false, 1556224151073374587], [3150220818285335163, "url", false, 6750346232297437404], [3712811570531045576, "byteorder", false, 9885777067248689192], [4359956005902820838, "utf8", false, 938278639461176274], [5986029879202738730, "log", false, 13813544180719911649], [6163892036024256188, "httparse", false, 11029079260025303864], [7489145127516980061, "bytes", false, 6471529210848970878], [8008191657135824715, "thiserror", false, 18229956334786815544], [9010263965687315507, "http", false, 5397628580711568589], [10724389056617919257, "sha1", false, 11552010542542413205], [13208667028893622512, "rand", false, 13984995954636255743]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-4a237495748c99b5/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}