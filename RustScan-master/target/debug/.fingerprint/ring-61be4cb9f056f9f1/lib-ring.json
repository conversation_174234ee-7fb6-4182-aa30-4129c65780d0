{"rustc": 16542739221701554903, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"std\", \"wasm32_unknown_unknown_js\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 5347358027863023418, "path": 915818621336271105, "deps": [[7670211519503158651, "getrandom", false, 16698323780386473653], [8995469080876806959, "untrusted", false, 6558222114812573113], [10411997081178400487, "cfg_if", false, 4021780138153559489], [13735576255540782179, "libc", false, 16161963579504566563], [14408907982612172527, "build_script_build", false, 6502765994127379058]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ring-61be4cb9f056f9f1/dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}