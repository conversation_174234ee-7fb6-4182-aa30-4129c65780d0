import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Button, Typography, Modal, message } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ScanOutlined,
  DatabaseOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CrownOutlined,
  EyeOutlined,
  MonitorOutlined,
} from '@ant-design/icons';

import { useTaskStore, useWebSocketStore } from '@/hooks/useStore';
import { useAuthStore, canManageUsers } from '@/store/auth';
import { UserRole } from '@/types';
import ConnectionStatus from '@/components/Common/ConnectionStatus';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  
  const { activeTasks } = useTaskStore();
  const { connected, logs } = useWebSocketStore();
  const { user, logout } = useAuthStore();

  // 侧边栏菜单项
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: 'tasks-menu',
      icon: <ScanOutlined />,
      label: '扫描任务',
      children: [
        {
          key: '/tasks',
          label: '任务列表',
        },
        {
          key: '/tasks/create',
          label: '创建任务',
        },
      ],
    },
    {
      key: '/results',
      icon: <DatabaseOutlined />,
      label: '扫描结果',
    },
    // 只有管理员能看到用户管理
    ...(canManageUsers(user?.role || UserRole.Viewer) ? [{
      key: '/users',
      icon: <TeamOutlined />,
      label: '用户管理',
    }] : []),
    // 只有管理员能看到系统管理
    ...(canManageUsers(user?.role || UserRole.Viewer) ? [{
      key: '/system',
      icon: <MonitorOutlined />,
      label: '系统管理',
    }] : []),
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'about',
      icon: <InfoCircleOutlined />,
      label: '关于',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key.startsWith('/')) {
      navigate(key);
    }
  };

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/profile');
        break;
      case 'about':
        Modal.info({
          title: '关于 RustScan Web',
          content: (
            <div>
              <p>版本: 2.4.1</p>
              <p>RustScan Web 是一个强大的网络安全扫描平台</p>
              <p>集成了多种安全工具，提供实时扫描监控</p>
            </div>
          ),
        });
        break;
      case 'logout':
        Modal.confirm({
          title: '确认退出',
          content: '您确定要退出登录吗？',
          onOk: () => {
            logout();
            message.success('已退出登录');
            navigate('/login');
          },
        });
        break;
    }
  };

  // 获取未读通知数量
  const unreadNotifications = logs.filter(log => 
    log.level === 'ERROR' || log.level === 'WARN'
  ).length;

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={240}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        {/* Logo 区域 */}
        <div
          style={{
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'flex-start',
            padding: collapsed ? 0 : '0 24px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          {collapsed ? (
            <ScanOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <ScanOutlined style={{ fontSize: 24, color: '#1890ff' }} />
              <Text strong style={{ color: '#1890ff', fontSize: 18 }}>
                RustScan Web
              </Text>
            </div>
          )}
        </div>

        {/* 导航菜单 */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          defaultOpenKeys={['tasks-menu']}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />

        {/* 底部状态 */}
        {!collapsed && (
          <div
            style={{
              position: 'absolute',
              bottom: 16,
              left: 16,
              right: 16,
            }}
          >
            <ConnectionStatus connected={connected} />
            {activeTasks.length > 0 && (
              <div style={{ marginTop: 8, color: '#1890ff', fontSize: 12 }}>
                {activeTasks.length} 个任务运行中
              </div>
            )}
          </div>
        )}
      </Sider>

      {/* 主内容区域 */}
      <Layout style={{ marginLeft: collapsed ? 80 : 240, transition: 'all 0.2s' }}>
        {/* 顶部导航栏 */}
        <Header
          style={{
            padding: '0 24px',
            background: '#fff',
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            position: 'fixed',
            top: 0,
            right: 0,
            left: collapsed ? 80 : 240,
            zIndex: 1000,
            transition: 'all 0.2s',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            {/* 折叠/展开按钮 */}
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: 16, width: 32, height: 32 }}
            />

            {/* 活动任务指示器 */}
            {activeTasks.length > 0 && (
              <Badge count={activeTasks.length} color="#52c41a">
                <ScanOutlined style={{ fontSize: 16, color: '#52c41a' }} />
              </Badge>
            )}
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            {/* 通知图标 */}
            <Badge count={unreadNotifications} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: 16, width: 32, height: 32 }}
                onClick={() => {
                  // 打开通知面板
                }}
              />
            </Badge>

            {/* 用户头像和菜单 */}
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 8,
                  cursor: 'pointer',
                  padding: '4px 8px',
                  borderRadius: 6,
                  transition: 'background 0.2s',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#f5f5f5';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                }}
              >
                <Avatar 
                  size="small" 
                  style={{ 
                    backgroundColor: user?.role === UserRole.Admin ? '#f56a00' :
                                   user?.role === UserRole.User ? '#1890ff' : '#52c41a'
                  }}
                >
                  {user?.role === UserRole.Admin ? <CrownOutlined /> :
                   user?.role === UserRole.User ? <UserOutlined /> : <EyeOutlined />}
                </Avatar>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Text style={{ fontSize: '14px', lineHeight: 1.2 }}>{user?.username}</Text>
                  <Text type="secondary" style={{ fontSize: '12px', lineHeight: 1.2 }}>
                    {user?.role === UserRole.Admin ? '管理员' :
                     user?.role === UserRole.User ? '用户' : '观察者'}
                  </Text>
                </div>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 页面内容 */}
        <Content
          style={{
            margin: '88px 24px 24px',
            minHeight: 'calc(100vh - 112px)',
            background: '#f5f5f5',
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppLayout;