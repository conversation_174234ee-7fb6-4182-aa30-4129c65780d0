import { useEffect, useRef, useState, useCallback } from 'react';
import { useTaskStore, useWebSocketStore } from './useStore';
import type { TaskUpdate, LogMessage, WebSocketMessage } from '@/types';

export interface WebSocketMessageData {
  message_type: string;
  task_id?: string;
  data: any;
  timestamp: string;
}

export const useWebSocket = (enabled = true) => {
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const healthCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [connected, setConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const maxReconnectAttempts = 5;
  const reconnectInterval = 3000;
  
  const { updateTask } = useTaskStore();
  const { setConnected: setWebSocketConnected, addLog } = useWebSocketStore();

  const connect = useCallback(() => {
    if (!enabled) {
      return;
    }

    // 如果已经有连接且状态正常，不要重复连接
    if (wsRef.current && (
      wsRef.current.readyState === WebSocket.OPEN ||
      wsRef.current.readyState === WebSocket.CONNECTING
    )) {
      return;
    }

    // 清理旧连接
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    try {
      const ws = new WebSocket('ws://localhost:8080/ws');
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('✅ WebSocket connected');
        setConnected(true);
        setWebSocketConnected(true);
        setReconnectAttempts(0);

        // 启动连接健康检查
        if (healthCheckTimeoutRef.current) {
          clearTimeout(healthCheckTimeoutRef.current);
        }
        healthCheckTimeoutRef.current = setTimeout(() => {
          // 如果60秒内没有收到任何消息，认为连接可能有问题
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            ping();
          }
        }, 60000);
      };

      ws.onclose = (event) => {
        if (event.code !== 1000) {
          console.warn('🔌 WebSocket disconnected:', event.code, event.reason || 'Unknown reason');
        }
        setConnected(false);
        setWebSocketConnected(false);
        wsRef.current = null;

        // 清理之前的重连定时器
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }

        // 只在非正常关闭且启用状态下重连
        if (enabled && event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          const delay = reconnectInterval + (reconnectAttempts * 1000); // 递增延迟
          console.log(`🔄 WebSocket reconnecting in ${delay}ms (${reconnectAttempts + 1}/${maxReconnectAttempts})`);

          reconnectTimeoutRef.current = setTimeout(() => {
            setReconnectAttempts(prev => prev + 1);
            connect();
          }, delay);
        } else if (reconnectAttempts >= maxReconnectAttempts) {
          console.error('❌ WebSocket max reconnection attempts reached');
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnected(false);
        setWebSocketConnected(false);
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessageData = JSON.parse(event.data);

          // 只记录非通知类型的消息
          if (message.message_type !== 'notification') {
            console.log('📨 WebSocket message:', message.message_type, message.data);
          }

          // 重置健康检查定时器
          if (healthCheckTimeoutRef.current) {
            clearTimeout(healthCheckTimeoutRef.current);
            healthCheckTimeoutRef.current = setTimeout(() => {
              if (wsRef.current?.readyState === WebSocket.OPEN) {
                ping();
              }
            }, 60000);
          }

          switch (message.message_type) {
            case 'task_update':
              if (message.task_id) {
                updateTask(message.task_id, {
                  status: message.data.status,
                  progress: message.data.progress,
                  error: null,
                });
              }
              break;

            case 'log':
              if (message.task_id) {
                addLog({
                  task_id: message.task_id,
                  level: message.data.level,
                  message: message.data.message,
                  timestamp: message.timestamp,
                });
              }
              break;

            case 'scan_result':
              console.log('Scan result received:', message.data);
              break;

            case 'notification':
              // 只记录非连接成功的通知
              if (message.data.message !== 'WebSocket connected successfully') {
                console.log('🔔 System notification:', message.data);
              }
              break;

            default:
              console.log('Unknown message type:', message.message_type);
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setConnected(false);
      setWebSocketConnected(false);
    }
  }, [enabled, reconnectAttempts, updateTask, setWebSocketConnected, addLog]);

  const disconnect = useCallback(() => {
    // 清理所有定时器
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (healthCheckTimeoutRef.current) {
      clearTimeout(healthCheckTimeoutRef.current);
      healthCheckTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Client disconnect'); // 正常关闭
      wsRef.current = null;
    }
    setConnected(false);
    setWebSocketConnected(false);
    setReconnectAttempts(0); // 重置重连计数
  }, [setWebSocketConnected]);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    console.warn('WebSocket is not connected');
    return false;
  }, []);

  const subscribeToTask = useCallback((taskId: string) => {
    return sendMessage({
      message_type: 'subscribe',
      task_id: taskId,
    });
  }, [sendMessage]);

  const unsubscribeFromTask = useCallback((taskId: string) => {
    return sendMessage({
      message_type: 'unsubscribe',
      task_id: taskId,
    });
  }, [sendMessage]);

  const ping = useCallback(() => {
    return sendMessage({
      message_type: 'ping',
    });
  }, [sendMessage]);

  useEffect(() => {
    if (enabled) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [enabled]); // 移除connect和disconnect依赖，避免无限循环

  // 定期发送ping保持连接
  useEffect(() => {
    if (!connected || !enabled) return;

    const pingInterval = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        ping();
      }
    }, 30000); // 30秒心跳

    return () => clearInterval(pingInterval);
  }, [connected, enabled, ping]);

  return {
    connected,
    sendMessage,
    subscribeToTask,
    unsubscribeFromTask,
    ping,
  };
};