import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider, theme, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import App from './App';
import TestApp from './TestApp';
import { useSettingsStore } from '@/hooks/useStore';

import 'antd/dist/reset.css';
import './index.css';

// 设置 dayjs 中文
dayjs.locale('zh-cn');

// 创建 React Query 客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5分钟
      refetchOnWindowFocus: false,
    },
  },
});

// 主题配置组件
const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { theme: themeMode } = useSettingsStore();

  const getTheme = () => {
    if (themeMode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
        ? theme.darkAlgorithm
        : theme.defaultAlgorithm;
    }
    return themeMode === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm;
  };

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: getTheme(),
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
          wireframe: false,
        },
        components: {
          Layout: {
            siderBg: themeMode === 'dark' ? '#001529' : '#001529',
            triggerBg: themeMode === 'dark' ? '#1f1f1f' : '#002140',
          },
          Menu: {
            darkItemBg: '#001529',
            darkSubMenuItemBg: '#000c17',
            darkItemSelectedBg: '#1890ff',
          },
        },
      }}
    >
      <AntdApp>{children}</AntdApp>
    </ConfigProvider>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <ThemeProvider>
          <App />
        </ThemeProvider>
      </BrowserRouter>
    </QueryClientProvider>
  </React.StrictMode>
);