import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Typography,
  Space,
  Row,
  Col,
  Switch,
  InputNumber,
  Divider,
  Alert,
  App,
} from 'antd';
import { useMutation } from '@tanstack/react-query';
import { ArrowLeftOutlined, PlayCircleOutlined } from '@ant-design/icons';

import { taskApi } from '@/api';
import { CreateTaskRequest, ScanType, ScanMode, TargetType, UserAgentType } from '@/types';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CreateTask: React.FC = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [selectedType, setSelectedType] = useState<ScanType>(ScanType.Standard);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // 创建任务
  const createTaskMutation = useMutation({
    mutationFn: taskApi.createTask,
    onSuccess: (response) => {
      message.success('任务创建成功');
      navigate(`/tasks/${response.data.data?.id}`);
    },
    onError: (error: any) => {
      message.error('创建任务失败: ' + error.message);
    },
  });

  const onFinish = (values: any) => {
    const taskRequest: CreateTaskRequest = {
      target: values.target.trim(),
      task_type: values.task_type,
      config: showAdvanced ? {
        scan_mode: values.scan_mode,
        target_type: values.target_type,
        enable_port_scan: values.enable_port_scan,
        enable_service_detection: values.enable_service_detection,
        enable_dns_resolution: values.enable_dns_resolution,
        enable_subdomain_enum: values.enable_subdomain_enum,
        enable_web_crawling: values.enable_web_crawling,
        enable_vulnerability_scan: values.enable_vulnerability_scan,
        max_parallel_tasks: values.max_parallel_tasks,
        timeout_per_step: values.timeout_per_step,
        max_retries: values.max_retries,
        continue_on_error: values.continue_on_error,
        nmap_config: {
          scan_type: values.nmap_scan_type,
          timing: values.nmap_timing,
          threads: values.nmap_threads,
          host_timeout: values.nmap_host_timeout,
          enable_service_detection: values.nmap_enable_service_detection,
          enable_os_detection: values.nmap_enable_os_detection,
          stealth_mode: values.nmap_stealth_mode,
          aggressive_scan: values.nmap_aggressive_scan,
        },
        httpx_config: {
          threads: values.httpx_threads,
          timeout: values.httpx_timeout,
          retries: values.httpx_retries,
          user_agent_type: values.httpx_user_agent_type,
          follow_redirects: values.httpx_follow_redirects,
          tech_detect: values.httpx_tech_detect,
          screenshot: values.httpx_screenshot,
        },
        nuclei_config: {
          threads: values.nuclei_threads,
          rate_limit: values.nuclei_rate_limit,
          timeout: values.nuclei_timeout,
          severity: values.nuclei_severity || ['medium', 'high', 'critical'],
          templates: values.nuclei_templates || ['cves/', 'vulnerabilities/'],
          user_agent_type: values.nuclei_user_agent_type,
          update_templates: values.nuclei_update_templates,
          passive_scan: values.nuclei_passive_scan,
        },
      } : undefined,
    };

    createTaskMutation.mutate(taskRequest);
  };

  const scanTypeDescriptions = {
    [ScanType.Quick]: '快速扫描常见端口和高危漏洞，适合快速评估',
    [ScanType.Standard]: '标准扫描，平衡速度和覆盖率',
    [ScanType.Deep]: '深度扫描所有端口和漏洞，时间较长但最全面',
    [ScanType.WebFocused]: '专注于Web应用扫描，包含爬虫和Web漏洞检测',
    [ScanType.Custom]: '自定义扫描配置，可精确控制扫描参数',
  };

  return (
    <div style={{ padding: 24 }}>
      <Card>
        {/* 页面标题 */}
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/tasks')}
            >
              返回
            </Button>
            <div>
              <Title level={3} style={{ margin: 0 }}>
                创建扫描任务
              </Title>
              <Text type="secondary">
                配置扫描目标和参数，开始安全扫描
              </Text>
            </div>
          </Space>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            task_type: ScanType.Standard,
            scan_mode: ScanMode.Standard,
            target_type: TargetType.Mixed,
            enable_port_scan: true,
            enable_service_detection: true,
            enable_dns_resolution: true,
            enable_subdomain_enum: true,
            enable_web_crawling: true,
            enable_vulnerability_scan: true,
            max_parallel_tasks: 3,
            timeout_per_step: 1800,
            max_retries: 2,
            continue_on_error: true,
            nmap_scan_type: 'SYN',
            nmap_timing: 4,
            nmap_threads: 50,
            nmap_host_timeout: 300,
            nmap_enable_service_detection: true,
            nmap_enable_os_detection: false,
            nmap_stealth_mode: false,
            nmap_aggressive_scan: false,
            httpx_threads: 50,
            httpx_timeout: 10,
            httpx_retries: 2,
            httpx_user_agent_type: UserAgentType.Desktop,
            httpx_follow_redirects: true,
            httpx_tech_detect: true,
            httpx_screenshot: false,
            nuclei_threads: 25,
            nuclei_rate_limit: 150,
            nuclei_timeout: 10,
            nuclei_user_agent_type: UserAgentType.Desktop,
            nuclei_update_templates: false,
            nuclei_passive_scan: false,
          }}
        >
          <Row gutter={24}>
            <Col xs={24} lg={12}>
              {/* 基础配置 */}
              <Card title="基础配置" size="small" style={{ marginBottom: 16 }}>
                <Form.Item
                  label="扫描目标"
                  name="target"
                  rules={[
                    { required: true, message: '请输入扫描目标' },
                    {
                      pattern: /^(https?:\/\/|[\w.-]+|[\d.]+|[\da-fA-F:]+).*$/,
                      message: '请输入有效的URL、域名或IP地址',
                    },
                  ]}
                >
                  <Input
                    placeholder="例如: example.com, ***********, https://example.com"
                    size="large"
                  />
                </Form.Item>

                <Form.Item
                  label="扫描类型"
                  name="task_type"
                  rules={[{ required: true, message: '请选择扫描类型' }]}
                >
                  <Select
                    size="large"
                    onChange={(value) => setSelectedType(value)}
                  >
                    <Option value={ScanType.Quick}>快速扫描</Option>
                    <Option value={ScanType.Standard}>标准扫描</Option>
                    <Option value={ScanType.Deep}>深度扫描</Option>
                    <Option value={ScanType.WebFocused}>Web专项扫描</Option>
                    <Option value={ScanType.Custom}>自定义扫描</Option>
                  </Select>
                </Form.Item>

                <Alert
                  message={scanTypeDescriptions[selectedType]}
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              </Card>

              {/* 高级配置开关 */}
              <Card title="配置选项" size="small">
                <Form.Item>
                  <Space>
                    <Switch
                      checked={showAdvanced}
                      onChange={setShowAdvanced}
                    />
                    <Text>显示高级配置</Text>
                  </Space>
                </Form.Item>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              {/* 扫描模块 */}
              {showAdvanced && (
                <Card title="扫描模块" size="small" style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="enable_port_scan" valuePropName="checked">
                        <Space>
                          <Switch size="small" />
                          <Text>端口扫描</Text>
                        </Space>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="enable_service_detection" valuePropName="checked">
                        <Space>
                          <Switch size="small" />
                          <Text>服务检测</Text>
                        </Space>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="enable_dns_resolution" valuePropName="checked">
                        <Space>
                          <Switch size="small" />
                          <Text>DNS解析</Text>
                        </Space>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="enable_subdomain_enum" valuePropName="checked">
                        <Space>
                          <Switch size="small" />
                          <Text>子域名枚举</Text>
                        </Space>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="enable_web_crawling" valuePropName="checked">
                        <Space>
                          <Switch size="small" />
                          <Text>Web爬虫</Text>
                        </Space>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="enable_vulnerability_scan" valuePropName="checked">
                        <Space>
                          <Switch size="small" />
                          <Text>漏洞扫描</Text>
                        </Space>
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              )}

              {/* 全局参数 */}
              {showAdvanced && (
                <Card title="全局参数" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="并行任务数" name="max_parallel_tasks">
                        <InputNumber min={1} max={10} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="步骤超时(秒)" name="timeout_per_step">
                        <InputNumber min={300} max={7200} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="最大重试次数" name="max_retries">
                        <InputNumber min={0} max={5} style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="continue_on_error" valuePropName="checked">
                        <Space>
                          <Switch size="small" />
                          <Text>出错时继续</Text>
                        </Space>
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              )}
            </Col>
          </Row>

          {/* 工具配置 */}
          {showAdvanced && (
            <>
              <Divider>工具配置</Divider>
              
              <Row gutter={24}>
                {/* Nmap配置 */}
                <Col xs={24} lg={8}>
                  <Card title="Nmap配置" size="small">
                    <Form.Item label="扫描类型" name="nmap_scan_type">
                      <Select size="small">
                        <Option value="SYN">SYN扫描</Option>
                        <Option value="TCP">TCP连接扫描</Option>
                        <Option value="UDP">UDP扫描</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="时序模板" name="nmap_timing">
                      <Select size="small">
                        <Option value={3}>正常 (T3)</Option>
                        <Option value={4}>快速 (T4)</Option>
                        <Option value={5}>急速 (T5)</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="线程数" name="nmap_threads">
                      <InputNumber min={1} max={100} size="small" style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Form.Item name="nmap_stealth_mode" valuePropName="checked">
                      <Space>
                        <Switch size="small" />
                        <Text>隐蔽模式</Text>
                      </Space>
                    </Form.Item>
                  </Card>
                </Col>

                {/* HTTPx配置 */}
                <Col xs={24} lg={8}>
                  <Card title="HTTPx配置" size="small">
                    <Form.Item label="线程数" name="httpx_threads">
                      <InputNumber min={1} max={200} size="small" style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Form.Item label="超时时间" name="httpx_timeout">
                      <InputNumber min={5} max={60} size="small" style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Form.Item label="User-Agent类型" name="httpx_user_agent_type">
                      <Select size="small">
                        <Option value={UserAgentType.Desktop}>桌面浏览器</Option>
                        <Option value={UserAgentType.Mobile}>移动浏览器</Option>
                        <Option value={UserAgentType.Random}>随机</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item name="httpx_tech_detect" valuePropName="checked">
                      <Space>
                        <Switch size="small" />
                        <Text>技术检测</Text>
                      </Space>
                    </Form.Item>
                  </Card>
                </Col>

                {/* Nuclei配置 */}
                <Col xs={24} lg={8}>
                  <Card title="Nuclei配置" size="small">
                    <Form.Item label="线程数" name="nuclei_threads">
                      <InputNumber min={1} max={100} size="small" style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Form.Item label="速率限制" name="nuclei_rate_limit">
                      <InputNumber min={50} max={500} size="small" style={{ width: '100%' }} />
                    </Form.Item>
                    
                    <Form.Item label="严重性" name="nuclei_severity">
                      <Select mode="multiple" size="small" placeholder="选择严重性级别">
                        <Option value="info">信息</Option>
                        <Option value="low">低危</Option>
                        <Option value="medium">中危</Option>
                        <Option value="high">高危</Option>
                        <Option value="critical">严重</Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item name="nuclei_update_templates" valuePropName="checked">
                      <Space>
                        <Switch size="small" />
                        <Text>更新模板</Text>
                      </Space>
                    </Form.Item>
                  </Card>
                </Col>
              </Row>
            </>
          )}

          {/* 操作按钮 */}
          <div style={{ marginTop: 32, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => navigate('/tasks')}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                icon={<PlayCircleOutlined />}
                loading={createTaskMutation.isPending}
                size="large"
              >
                开始扫描
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default CreateTask;