import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Table,
  Tag,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tabs,
  Statistic,
  Progress,
  Tooltip,
  Badge,
  Divider,
  Alert,
  Spin
} from 'antd';
import {
  DatabaseOutlined,
  SecurityScanOutlined,
  BugOutlined,
  GlobalOutlined,
  SearchOutlined,
  DownloadOutlined,
  FilterOutlined,
  EyeOutlined,
  WarningOutlined,
  SafetyCertificateOutlined,
  ClusterOutlined,
  LinkOutlined
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, 
         PieChart, Pie, Cell, LineChart, Line, Scatter<PERSON>hart, Scatter, TreeMap } from 'recharts';
import { resultsApi } from '@/api';
import type { Host, Port, Vulnerability, WebAsset, Task } from '@/types';
import dayjs, { Dayjs } from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const Results: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchText, setSearchText] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string | undefined>();
  const [statusFilter, setStatusFilter] = useState<string | undefined>();
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [pageSize, setPageSize] = useState(10);

  // 获取统计数据
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['results-statistics'],
    queryFn: () => resultsApi.getStatistics(),
    refetchInterval: 30000,
  });

  // 获取主机数据
  const { data: hostsData, isLoading: hostsLoading } = useQuery({
    queryKey: ['hosts', searchText, statusFilter, dateRange],
    queryFn: () => {
      const params = new URLSearchParams();
      if (searchText) params.append('search', searchText);
      if (statusFilter) params.append('status', statusFilter);
      if (dateRange) {
        params.append('start_date', dateRange[0]?.toISOString() || '');
        params.append('end_date', dateRange[1]?.toISOString() || '');
      }
      return resultsApi.getHosts(params.toString());
    },
  });

  // 获取端口数据
  const { data: portsData } = useQuery({
    queryKey: ['ports', searchText, statusFilter],
    queryFn: () => {
      const params = new URLSearchParams();
      if (searchText) params.append('search', searchText);
      if (statusFilter) params.append('state', statusFilter);
      return resultsApi.getPorts(params.toString());
    },
  });

  // 获取漏洞数据
  const { data: vulnerabilitiesData } = useQuery({
    queryKey: ['vulnerabilities', searchText, severityFilter, dateRange],
    queryFn: () => {
      const params = new URLSearchParams();
      if (searchText) params.append('search', searchText);
      if (severityFilter) params.append('severity', severityFilter);
      if (dateRange) {
        params.append('start_date', dateRange[0]?.toISOString() || '');
        params.append('end_date', dateRange[1]?.toISOString() || '');
      }
      return resultsApi.getVulnerabilities(params.toString());
    },
  });

  // 获取Web资产数据
  const { data: webAssetsData } = useQuery({
    queryKey: ['web-assets', searchText, statusFilter],
    queryFn: () => {
      const params = new URLSearchParams();
      if (searchText) params.append('search', searchText);
      if (statusFilter) params.append('status', statusFilter);
      return resultsApi.getWebAssets(params.toString());
    },
  });

  const statistics = statsData?.data;
  const hosts = hostsData?.data?.items || [];
  const ports = portsData?.data?.items || [];
  const vulnerabilities = vulnerabilitiesData?.data?.items || [];
  const webAssets = webAssetsData?.data?.items || [];

  // 处理图表数据
  const portDistributionData = React.useMemo(() => {
    const portCounts: { [key: string]: number } = {};
    ports.forEach((port: Port) => {
      const key = `${port.port}/${port.protocol}`;
      portCounts[key] = (portCounts[key] || 0) + 1;
    });
    
    return Object.entries(portCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([port, count]) => ({ port, count }));
  }, [ports]);

  const vulnerabilityTrendData = React.useMemo(() => {
    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = dayjs().subtract(i, 'day');
      const dayVulns = vulnerabilities.filter((vuln: Vulnerability) =>
        dayjs(vuln.created_at).isSame(date, 'day')
      );
      
      return {
        date: date.format('MM-DD'),
        total: dayVulns.length,
        critical: dayVulns.filter(v => v.severity === 'critical').length,
        high: dayVulns.filter(v => v.severity === 'high').length,
        medium: dayVulns.filter(v => v.severity === 'medium').length,
        low: dayVulns.filter(v => v.severity === 'low').length,
      };
    }).reverse();
    
    return last30Days;
  }, [vulnerabilities]);

  const severityColors = {
    critical: '#f5222d',
    high: '#fa541c',
    medium: '#faad14',
    low: '#1890ff',
    info: '#52c41a',
  };

  // 主机表格列
  const hostColumns = [
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address',
      render: (ip: string) => <Text code>{ip}</Text>,
    },
    {
      title: '主机名',
      dataIndex: 'hostname',
      key: 'hostname',
      render: (hostname: string) => hostname || <Text type="secondary">-</Text>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'up' ? 'green' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '操作系统',
      dataIndex: 'os_name',
      key: 'os_name',
      render: (os: string) => os || <Text type="secondary">未知</Text>,
    },
    {
      title: '开放端口',
      dataIndex: 'port_count',
      key: 'port_count',
      render: (count: number) => (
        <Badge count={count} style={{ backgroundColor: '#52c41a' }} />
      ),
    },
    {
      title: '发现时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm'),
    },
  ];

  // 端口表格列
  const portColumns = [
    {
      title: 'IP地址',
      dataIndex: 'host_ip',
      key: 'host_ip',
      render: (ip: string) => <Text code>{ip}</Text>,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      render: (port: number, record: Port) => (
        <Text strong>{port}/{record.protocol}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      render: (state: string) => (
        <Tag color={state === 'open' ? 'green' : state === 'closed' ? 'red' : 'orange'}>
          {state.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '服务',
      dataIndex: 'service',
      key: 'service',
      render: (service: string) => service || <Text type="secondary">未知</Text>,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      render: (version: string) => version || <Text type="secondary">-</Text>,
    },
  ];

  // 漏洞表格列
  const vulnerabilityColumns = [
    {
      title: '严重性',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => (
        <Tag color={severityColors[severity as keyof typeof severityColors]}>
          {severity.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '漏洞名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (name: string) => <Text strong>{name}</Text>,
    },
    {
      title: 'CVE ID',
      dataIndex: 'cve_id',
      key: 'cve_id',
      render: (cve: string) => cve ? (
        <Tag color="purple">{cve}</Tag>
      ) : <Text type="secondary">-</Text>,
    },
    {
      title: 'CVSS评分',
      dataIndex: 'cvss_score',
      key: 'cvss_score',
      render: (score: number) => score ? (
        <Text strong style={{ color: score >= 7 ? '#f5222d' : score >= 4 ? '#fa541c' : '#52c41a' }}>
          {score.toFixed(1)}
        </Text>
      ) : <Text type="secondary">-</Text>,
    },
    {
      title: '影响主机',
      dataIndex: 'host_ip',
      key: 'host_ip',
      render: (ip: string) => <Text code>{ip}</Text>,
    },
    {
      title: '发现时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => dayjs(time).format('MM-DD HH:mm'),
    },
  ];

  const renderOverview = () => (
    <div>
      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="发现主机"
              value={statistics?.total_hosts || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
              loading={statsLoading}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                在线: {hosts.filter(h => h.status === 'up').length}
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="开放端口"
              value={statistics?.total_ports || 0}
              prefix={<SecurityScanOutlined />}
              valueStyle={{ color: '#52c41a' }}
              loading={statsLoading}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                服务: {ports.filter(p => p.service).length}
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Web资产"
              value={statistics?.total_web_assets || 0}
              prefix={<GlobalOutlined />}
              valueStyle={{ color: '#722ed1' }}
              loading={statsLoading}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                活跃: {webAssets.filter(w => w.status_code === 200).length}
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="发现漏洞"
              value={statistics?.total_vulnerabilities || 0}
              prefix={<BugOutlined />}
              valueStyle={{ color: '#f5222d' }}
              loading={statsLoading}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                高危: {vulnerabilities.filter(v => ['critical', 'high'].includes(v.severity)).length}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* 端口分布 */}
        <Col xs={24} lg={12}>
          <Card title="常见端口分布" extra={<Text type="secondary">TOP 20</Text>}>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={portDistributionData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="port" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <RechartsTooltip />
                <Bar dataKey="count" fill="#1890ff" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 漏洞趋势 */}
        <Col xs={24} lg={12}>
          <Card title="30天漏洞发现趋势" extra={<Text type="secondary">按严重性分类</Text>}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={vulnerabilityTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line type="monotone" dataKey="critical" stroke="#f5222d" strokeWidth={2} name="严重" />
                <Line type="monotone" dataKey="high" stroke="#fa541c" strokeWidth={2} name="高危" />
                <Line type="monotone" dataKey="medium" stroke="#faad14" strokeWidth={2} name="中危" />
                <Line type="monotone" dataKey="low" stroke="#1890ff" strokeWidth={2} name="低危" />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 风险评估 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card title="整体风险评估" bordered>
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Progress
                type="circle"
                percent={vulnerabilities.filter(v => ['critical', 'high'].includes(v.severity)).length > 0 ? 85 : 
                        vulnerabilities.filter(v => v.severity === 'medium').length > 5 ? 65 : 35}
                status={vulnerabilities.filter(v => ['critical', 'high'].includes(v.severity)).length > 0 ? 'exception' : 'normal'}
                size={120}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': vulnerabilities.filter(v => ['critical', 'high'].includes(v.severity)).length > 0 ? '#f5222d' : '#87d068',
                }}
              />
              <div style={{ marginTop: 16 }}>
                <Text strong>
                  {vulnerabilities.filter(v => ['critical', 'high'].includes(v.severity)).length > 0 ? '高风险' : 
                   vulnerabilities.filter(v => v.severity === 'medium').length > 5 ? '中等风险' : '低风险'}
                </Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          <Card title="安全建议" bordered>
            <Space direction="vertical" style={{ width: '100%' }}>
              {vulnerabilities.filter(v => v.severity === 'critical').length > 0 && (
                <Alert
                  message="发现严重漏洞"
                  description={`检测到 ${vulnerabilities.filter(v => v.severity === 'critical').length} 个严重漏洞，建议立即修复`}
                  type="error"
                  showIcon
                  icon={<WarningOutlined />}
                />
              )}
              
              {ports.filter(p => ['21', '23', '135', '139', '445'].includes(p.port.toString())).length > 0 && (
                <Alert
                  message="发现敏感端口"
                  description="检测到高风险端口开放，建议审查是否需要关闭"
                  type="warning"
                  showIcon
                />
              )}
              
              {hosts.filter(h => !h.os_name).length > 0 && (
                <Alert
                  message="主机指纹不完整"
                  description="部分主机操作系统信息缺失，建议进行深度扫描"
                  type="info"
                  showIcon
                />
              )}
              
              <Alert
                message="扫描完成"
                description={`已完成对 ${hosts.length} 台主机的安全扫描，发现 ${ports.length} 个端口和 ${vulnerabilities.length} 个安全问题`}
                type="success"
                showIcon
                icon={<SafetyCertificateOutlined />}
              />
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题和筛选器 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={2} style={{ margin: 0 }}>
            扫描结果
          </Title>
          <Space>
            <Button type="primary" icon={<DownloadOutlined />}>
              导出报告
            </Button>
          </Space>
        </div>
        
        {/* 筛选器 */}
        <Card size="small">
          <Row gutter={16}>
            <Col xs={24} sm={8} md={6}>
              <Input
                placeholder="搜索IP、主机名、漏洞等"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </Col>
            <Col xs={24} sm={8} md={4}>
              <Select
                placeholder="严重性"
                value={severityFilter}
                onChange={setSeverityFilter}
                allowClear
                style={{ width: '100%' }}
              >
                <Option value="critical">严重</Option>
                <Option value="high">高危</Option>
                <Option value="medium">中危</Option>
                <Option value="low">低危</Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} md={4}>
              <Select
                placeholder="状态"
                value={statusFilter}
                onChange={setStatusFilter}
                allowClear
                style={{ width: '100%' }}
              >
                <Option value="up">在线</Option>
                <Option value="down">离线</Option>
                <Option value="open">开放</Option>
                <Option value="closed">关闭</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Button icon={<FilterOutlined />} block>
                高级筛选
              </Button>
            </Col>
          </Row>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'overview',
            label: (
              <span>
                <ClusterOutlined />
                概览
              </span>
            ),
            children: renderOverview(),
          },
          {
            key: 'hosts',
            label: (
              <span>
                <DatabaseOutlined />
                主机 ({hosts.length})
              </span>
            ),
            children: (
              <Card>
                <Table
                  columns={hostColumns}
                  dataSource={hosts}
                  rowKey="id"
                  loading={hostsLoading}
                  pagination={{
                    pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                    onShowSizeChange: (_, size) => setPageSize(size),
                  }}
                  scroll={{ x: 800 }}
                />
              </Card>
            ),
          },
          {
            key: 'ports',
            label: (
              <span>
                <SecurityScanOutlined />
                端口 ({ports.length})
              </span>
            ),
            children: (
              <Card>
                <Table
                  columns={portColumns}
                  dataSource={ports}
                  rowKey="id"
                  pagination={{
                    pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                  scroll={{ x: 800 }}
                />
              </Card>
            ),
          },
        
        <TabPane
          tab={
            <span>
              <BugOutlined />
              漏洞 ({vulnerabilities.length})
            </span>
          }
          key="vulnerabilities"
        >
          <Card>
            <Table
              columns={vulnerabilityColumns}
              dataSource={vulnerabilities}
              rowKey="id"
              pagination={{
                pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              scroll={{ x: 800 }}
              expandable={{
                expandedRowRender: (record: Vulnerability) => (
                  <div style={{ padding: 16 }}>
                    <Title level={5}>漏洞描述</Title>
                    <Text>{record.description || '暂无描述'}</Text>
                    <Divider />
                    <Row gutter={16}>
                      <Col span={8}>
                        <Text strong>漏洞ID: </Text>
                        <Text code>{record.vulnerability_id}</Text>
                      </Col>
                      <Col span={8}>
                        <Text strong>CVSS评分: </Text>
                        <Text>{record.cvss_score || 'N/A'}</Text>
                      </Col>
                      <Col span={8}>
                        <Text strong>发现时间: </Text>
                        <Text>{dayjs(record.created_at).format('YYYY-MM-DD HH:mm:ss')}</Text>
                      </Col>
                    </Row>
                  </div>
                ),
                rowExpandable: () => true,
              }}
            />
          </Card>
        </TabPane>
        
        <TabPane
          tab={
            <span>
              <LinkOutlined />
              Web资产 ({webAssets.length})
            </span>
          }
          key="web-assets"
        >
          <Card>
            <Table
              columns={[
                {
                  title: 'URL',
                  dataIndex: 'url',
                  key: 'url',
                  ellipsis: true,
                  render: (url: string) => (
                    <a href={url} target="_blank" rel="noopener noreferrer">
                      {url}
                    </a>
                  ),
                },
                {
                  title: '状态码',
                  dataIndex: 'status_code',
                  key: 'status_code',
                  render: (code: number) => (
                    <Tag color={code === 200 ? 'green' : code >= 400 ? 'red' : 'orange'}>
                      {code}
                    </Tag>
                  ),
                },
                {
                  title: '标题',
                  dataIndex: 'title',
                  key: 'title',
                  ellipsis: true,
                },
                {
                  title: '技术栈',
                  dataIndex: 'technologies',
                  key: 'technologies',
                  render: (techs: string[]) => (
                    <div>
                      {techs?.slice(0, 3).map(tech => (
                        <Tag key={tech} size="small">{tech}</Tag>
                      ))}
                      {techs?.length > 3 && <Text type="secondary">+{techs.length - 3}</Text>}
                    </div>
                  ),
                },
                {
                  title: '发现时间',
                  dataIndex: 'created_at',
                  key: 'created_at',
                  render: (time: string) => dayjs(time).format('MM-DD HH:mm'),
                },
              ]}
              dataSource={webAssets}
              rowKey="id"
              pagination={{
                pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              scroll={{ x: 800 }}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Results;