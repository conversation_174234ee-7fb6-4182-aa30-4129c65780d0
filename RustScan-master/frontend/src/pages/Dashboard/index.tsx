import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Progress, Table, Tag, Button, Typography, Space, Timeline } from 'antd';
import { useQuery } from '@tanstack/react-query';
import {
  ScanOutlined,
  DatabaseOutlined,
  SecurityScanOutlined,
  BugOutlined,
  ReloadOutlined,
  ExportOutlined,
  WifiOutlined,
  DisconnectOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

import { taskApi, resultsApi } from '@/api';
import { useTaskStore, useWebSocketStore } from '@/hooks/useStore';
import { TaskStatus, VulnerabilitySeverity } from '@/types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { tasks, activeTasks } = useTaskStore();
  const { connected: wsConnected, logs } = useWebSocketStore();
  const [scanActivity, setScanActivity] = useState<any[]>([]);

  // 获取统计信息
  const { data: statsData, isLoading: statsLoading, refetch: refetchStats } = useQuery({
    queryKey: ['statistics'],
    queryFn: () => resultsApi.getStatistics(),
    refetchInterval: 30000,
  });

  // 获取最近的漏洞
  const { data: vulnerabilitiesData, refetch: refetchVulns } = useQuery({
    queryKey: ['recent-vulnerabilities'],
    queryFn: () => resultsApi.getVulnerabilities(),
    refetchInterval: 30000,
  });

  // 获取任务历史
  const { data: taskHistoryData } = useQuery({
    queryKey: ['task-history'],
    queryFn: () => taskApi.getTasks(1, 20),
    refetchInterval: 60000,
  });

  const statistics = statsData?.data?.data;
  const recentVulnerabilities = vulnerabilitiesData?.data?.items || [];
  const taskHistory = taskHistoryData?.data?.items || [];

  // 计算任务统计
  const taskStats = {
    total: tasks.length,
    running: tasks.filter(t => t.status === TaskStatus.Running).length,
    completed: tasks.filter(t => t.status === TaskStatus.Completed).length,
    failed: tasks.filter(t => t.status === TaskStatus.Failed).length,
  };

  // 计算扫描活动数据
  useEffect(() => {
    const now = dayjs();
    const activityData = [];
    
    for (let i = 23; i >= 0; i--) {
      const hour = now.subtract(i, 'hour');
      const hourTasks = taskHistory.filter(task => 
        dayjs(task.created_at).isSame(hour, 'hour')
      );
      
      activityData.push({
        hour: hour.format('HH:mm'),
        scans: hourTasks.length,
        completed: hourTasks.filter(t => t.status === TaskStatus.Completed).length,
        failed: hourTasks.filter(t => t.status === TaskStatus.Failed).length,
      });
    }
    
    setScanActivity(activityData);
  }, [taskHistory]);

  // 漏洞严重性颜色映射
  const severityColors = {
    [VulnerabilitySeverity.Critical]: '#f5222d',
    [VulnerabilitySeverity.High]: '#fa541c',
    [VulnerabilitySeverity.Medium]: '#faad14',
    [VulnerabilitySeverity.Low]: '#1890ff',
    [VulnerabilitySeverity.Info]: '#52c41a',
  };

  // 准备饼图数据
  const pieData = statistics?.vulnerability_by_severity ? 
    Object.entries(statistics.vulnerability_by_severity).map(([severity, count]) => ({
      name: severity.toUpperCase(),
      value: count,
      color: severityColors[severity as VulnerabilitySeverity]
    })) : [];

  // 获取最近的日志活动
  const recentLogs = Object.values(logs)
    .flat()
    .sort((a, b) => dayjs(b.timestamp).valueOf() - dayjs(a.timestamp).valueOf())
    .slice(0, 5);

  // 漏洞表格列定义
  const vulnerabilityColumns = [
    {
      title: '严重性',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: VulnerabilitySeverity) => (
        <Tag color={severity === VulnerabilitySeverity.Critical ? 'red' : 
                   severity === VulnerabilitySeverity.High ? 'orange' : 
                   severity === VulnerabilitySeverity.Medium ? 'gold' : 'blue'}>
          {severity.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '漏洞标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '漏洞ID',
      dataIndex: 'vulnerability_id',
      key: 'vulnerability_id',
      width: 150,
      render: (id: string) => (
        <Text code style={{ fontSize: 12 }}>{id}</Text>
      ),
    },
    {
      title: 'CVE ID',
      dataIndex: 'cve_id',
      key: 'cve_id',
      width: 120,
      render: (cveId: string) => cveId ? (
        <Tag color="purple">{cveId}</Tag>
      ) : (
        <Text type="secondary">-</Text>
      ),
    },
    {
      title: 'CVSS',
      dataIndex: 'cvss_score',
      key: 'cvss_score',
      width: 80,
      render: (score: number) => score ? (
        <Text strong>{score.toFixed(1)}</Text>
      ) : (
        <Text type="secondary">-</Text>
      ),
    },
    {
      title: '发现时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time: string) => (
        <Text type="secondary">{dayjs(time).format('MM-DD HH:mm')}</Text>
      ),
    },
  ];

  const handleRefreshAll = () => {
    refetchStats();
    refetchVulns();
    window.location.reload();
  };

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题和连接状态 */}
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            仪表板
          </Title>
          <Text type="secondary">
            实时监控扫描任务状态和安全发现
          </Text>
        </div>
        <Space>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {wsConnected ? (
              <Tag color="success" icon={<WifiOutlined />}>
                实时连接
              </Tag>
            ) : (
              <Tag color="error" icon={<DisconnectOutlined />}>
                连接断开
              </Tag>
            )}
          </div>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />}
            onClick={handleRefreshAll}
          >
            刷新全部
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={taskStats.total}
              prefix={<ScanOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                运行中: {taskStats.running} | 已完成: {taskStats.completed}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="发现主机"
              value={statistics?.total_hosts || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#52c41a' }}
              loading={statsLoading}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                开放端口: {statistics?.total_ports || 0}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Web资产"
              value={statistics?.total_web_assets || 0}
              prefix={<SecurityScanOutlined />}
              valueStyle={{ color: '#722ed1' }}
              loading={statsLoading}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                爬取页面: {statistics?.total_pages || 0}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="发现漏洞"
              value={statistics?.total_vulnerabilities || 0}
              prefix={<BugOutlined />}
              valueStyle={{ color: '#f5222d' }}
              loading={statsLoading}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                高危: {statistics?.vulnerability_by_severity?.high || 0}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 扫描活动图表和活动任务 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* 24小时扫描活动 */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <span>
                <RiseOutlined style={{ marginRight: 8 }} />
                24小时扫描活动
              </span>
            }
            extra={<Text type="secondary">最近24小时</Text>}
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={scanActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="scans" 
                  stroke="#1890ff" 
                  name="扫描任务"
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="completed" 
                  stroke="#52c41a" 
                  name="已完成"
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="failed" 
                  stroke="#f5222d" 
                  name="失败"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 活动任务 */}
        <Col xs={24} lg={8}>
          <Card
            title="活动任务"
            extra={
              <Button 
                type="text" 
                icon={<ReloadOutlined />} 
                size="small"
                onClick={() => window.location.reload()}
              >
                刷新
              </Button>
            }
          >
            {activeTasks.length === 0 ? (
              <div
                style={{
                  textAlign: 'center',
                  padding: '40px 0',
                  color: '#999',
                }}
              >
                <ScanOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <div>当前没有运行中的任务</div>
              </div>
            ) : (
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                {activeTasks.slice(0, 5).map((task) => (
                  <div key={task.id}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginBottom: 4,
                      }}
                    >
                      <Text ellipsis style={{ maxWidth: 200 }}>
                        {task.target}
                      </Text>
                      <Text>{task.progress}%</Text>
                    </div>
                    <Progress 
                      percent={task.progress} 
                      size="small" 
                      status={task.status === TaskStatus.Failed ? 'exception' : 'active'}
                    />
                  </div>
                ))}
              </Space>
            )}
          </Card>
        </Col>
      </Row>

      {/* 漏洞分布和实时活动 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* 漏洞严重性分布饼图 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <BarChartOutlined style={{ marginRight: 8 }} />
                漏洞严重性分布
              </span>
            }
          >
            {pieData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div
                style={{
                  textAlign: 'center',
                  padding: '60px 0',
                  color: '#999',
                }}
              >
                暂无漏洞数据
              </div>
            )}
          </Card>
        </Col>

        {/* 实时活动日志 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <ClockCircleOutlined style={{ marginRight: 8 }} />
                实时活动
              </span>
            }
            extra={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {wsConnected ? (
                  <Tag color="success">实时</Tag>
                ) : (
                  <Tag color="default">离线</Tag>
                )}
              </div>
            }
          >
            {recentLogs.length > 0 ? (
              <Timeline style={{ marginTop: 16, maxHeight: 240, overflow: 'auto' }}>
                {recentLogs.map((log, index) => (
                  <Timeline.Item
                    key={index}
                    color={log.level === 'error' ? 'red' : log.level === 'warn' ? 'orange' : 'blue'}
                  >
                    <div style={{ fontSize: 12 }}>
                      <Tag 
                        color={log.level === 'error' ? 'red' : log.level === 'warn' ? 'orange' : 'blue'}
                        size="small"
                      >
                        {log.level.toUpperCase()}
                      </Tag>
                      <Text type="secondary">
                        {dayjs(log.timestamp).format('HH:mm:ss')}
                      </Text>
                    </div>
                    <div style={{ fontSize: 13, marginTop: 4 }}>
                      {log.message.length > 100 ? 
                        `${log.message.substring(0, 100)}...` : 
                        log.message
                      }
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <div
                style={{
                  textAlign: 'center',
                  padding: '60px 0',
                  color: '#999',
                }}
              >
                <ClockCircleOutlined style={{ fontSize: 32, marginBottom: 8 }} />
                <div>暂无活动日志</div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 最近发现的漏洞 */}
      <Card
        title="最近发现的漏洞"
        extra={
          <Space>
            <Button 
              type="text" 
              icon={<ExportOutlined />} 
              size="small"
            >
              导出
            </Button>
            <Button 
              type="text" 
              icon={<ReloadOutlined />} 
              size="small"
              onClick={refetchVulns}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={vulnerabilityColumns}
          dataSource={recentVulnerabilities}
          rowKey="id"
          size="small"
          pagination={false}
          scroll={{ x: 800 }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', color: '#999' }}>
                <BugOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <div>暂无漏洞发现</div>
              </div>
            ),
          }}
        />
      </Card>
    </div>
  );
};

export default Dashboard;