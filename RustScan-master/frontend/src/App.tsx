import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout, Spin } from 'antd';

import Login from '@/pages/Login';
import Dashboard from '@/pages/Dashboard';
import AppLayout from '@/components/Layout/AppLayout';
import { useAuthStore } from '@/store/auth';

// 懒加载页面组件
const TaskList = React.lazy(() => import('@/pages/Tasks/TaskList'));
const CreateTask = React.lazy(() => import('@/pages/Tasks/CreateTask'));
const TaskDetail = React.lazy(() => import('@/pages/Tasks/TaskDetail'));
const Results = React.lazy(() => import('@/pages/Results'));
const Settings = React.lazy(() => import('@/pages/Settings'));
const UserManagement = React.lazy(() => import('@/pages/UserManagement'));
const SystemManagement = React.lazy(() => import('@/pages/SystemManagement'));
const NotFound = React.lazy(() => import('@/pages/NotFound'));

const App: React.FC = () => {
  const { isAuthenticated, checkAuth } = useAuthStore();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      await checkAuth();
      setIsInitializing(false);
    };

    initializeAuth();
  }, [checkAuth]);

  // 显示加载状态
  if (isInitializing) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route
        path="/*"
        element={
          isAuthenticated ? (
            <AppLayout>
              <React.Suspense fallback={
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '50vh'
                }}>
                  <Spin size="large" />
                </div>
              }>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/tasks" element={<TaskList />} />
                  <Route path="/tasks/create" element={<CreateTask />} />
                  <Route path="/tasks/:taskId" element={<TaskDetail />} />
                  <Route path="/results" element={<Results />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/users" element={<UserManagement />} />
                  <Route path="/system" element={<SystemManagement />} />
                  <Route path="/404" element={<NotFound />} />
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </React.Suspense>
            </AppLayout>
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />
    </Routes>
  );
};

export default App;