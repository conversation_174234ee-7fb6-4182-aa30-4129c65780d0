body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #2f2f2f;
}

.dark ::-webkit-scrollbar-thumb {
  background: #666;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #888;
}

/* 终端样式 */
.terminal-container {
  background: #1e1e1e;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
  color: #f8f8f2;
  height: 400px;
  overflow-y: auto;
}

.terminal-container.light {
  background: #f8f8f8;
  color: #2d3748;
}

/* 状态标签样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.running {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.completed {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.failed {
  background: #fee2e2;
  color: #991b1b;
}

.status-badge.stopped {
  background: #f3f4f6;
  color: #374151;
}

/* 严重性标签样式 */
.severity-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.severity-badge.info {
  background: #e0f2fe;
  color: #01579b;
}

.severity-badge.low {
  background: #f3e5f5;
  color: #4a148c;
}

.severity-badge.medium {
  background: #fff3e0;
  color: #e65100;
}

.severity-badge.high {
  background: #ffebee;
  color: #c62828;
}

.severity-badge.critical {
  background: #ffcdd2;
  color: #b71c1c;
}

/* 进度条样式 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

/* 卡片样式 */
.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.metric-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.metric-card .metric-value {
  font-size: 32px;
  font-weight: 600;
  margin: 8px 0;
}

.metric-card .metric-label {
  color: #666;
  font-size: 14px;
}

.metric-card .metric-change {
  font-size: 12px;
  margin-top: 4px;
}

.metric-change.positive {
  color: #52c41a;
}

.metric-change.negative {
  color: #ff4d4f;
}

/* 表格样式 */
.results-table .ant-table-cell {
  padding: 12px 16px;
}

.results-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
}

.results-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 标签样式 */
.tech-tag {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #389e0d;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin: 2px;
  display: inline-block;
}

.port-tag {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #096dd9;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin: 2px;
  display: inline-block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-card {
    padding: 16px;
  }
  
  .metric-card .metric-value {
    font-size: 24px;
  }
  
  .results-table .ant-table-cell {
    padding: 8px 12px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* 高亮搜索结果 */
.highlight {
  background: #fffbe6;
  border: 1px solid #ffe58f;
  padding: 0 2px;
  border-radius: 2px;
}

/* Loading 样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  flex-direction: column;
  gap: 16px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

/* Empty 状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: #ccc;
}

/* 菜单样式优化 */
.ant-menu-dark .ant-menu-item,
.ant-menu-dark .ant-menu-submenu-title {
  cursor: pointer;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.2) !important;
}