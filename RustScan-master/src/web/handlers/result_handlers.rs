use crate::handlers::task_handlers::ApiResponse;
use crate::services::database::Database;
use actix_web::{get, web, HttpResponse, Result};
use uuid::Uuid;

#[get("/tasks/{task_id}/hosts")]
async fn get_hosts(db: web::Data<Database>, path: web::Path<Uuid>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    match db.get_hosts_by_task(task_id).await {
        Ok(hosts) => Ok(HttpResponse::Ok().json(ApiResponse::success(hosts))),
        Err(e) => {
            log::error!("Failed to get hosts: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/tasks/{task_id}/ports")]
async fn get_ports(
    db: web::Data<Database>,
    path: web::Path<Uuid>,
    query: web::Query<PortQuery>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    match db.get_ports_by_task(task_id, query.host_id).await {
        Ok(ports) => Ok(HttpResponse::Ok().json(ApiResponse::success(ports))),
        Err(e) => {
            log::error!("Failed to get ports: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/tasks/{task_id}/vulnerabilities")]
async fn get_vulnerabilities(
    db: web::Data<Database>,
    path: web::Path<Uuid>,
    query: web::Query<VulnQuery>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    match db
        .get_vulnerabilities_by_task(task_id, query.severity.clone())
        .await
    {
        Ok(vulnerabilities) => Ok(HttpResponse::Ok().json(ApiResponse::success(vulnerabilities))),
        Err(e) => {
            log::error!("Failed to get vulnerabilities: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/statistics")]
async fn get_statistics(
    db: web::Data<Database>,
    query: web::Query<StatisticsQuery>,
) -> Result<HttpResponse> {
    match db.get_scan_statistics(query.task_id).await {
        Ok(stats) => Ok(HttpResponse::Ok().json(ApiResponse::success(stats))),
        Err(e) => {
            log::error!("Failed to get statistics: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/hosts")]
async fn get_all_hosts(
    db: web::Data<Database>,
    query: web::Query<PaginationQuery>,
) -> Result<HttpResponse> {
    match db
        .get_all_hosts(query.page.unwrap_or(1), query.size.unwrap_or(20))
        .await
    {
        Ok(hosts) => Ok(HttpResponse::Ok().json(ApiResponse::success(hosts))),
        Err(e) => {
            log::error!("Failed to get all hosts: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/ports")]
async fn get_all_ports(
    db: web::Data<Database>,
    query: web::Query<PaginationQuery>,
) -> Result<HttpResponse> {
    match db
        .get_all_ports(query.page.unwrap_or(1), query.size.unwrap_or(20))
        .await
    {
        Ok(ports) => Ok(HttpResponse::Ok().json(ApiResponse::success(ports))),
        Err(e) => {
            log::error!("Failed to get all ports: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/web-assets")]
async fn get_all_web_assets(
    db: web::Data<Database>,
    query: web::Query<PaginationQuery>,
) -> Result<HttpResponse> {
    match db
        .get_all_web_assets(query.page.unwrap_or(1), query.size.unwrap_or(20))
        .await
    {
        Ok(web_assets) => Ok(HttpResponse::Ok().json(ApiResponse::success(web_assets))),
        Err(e) => {
            log::error!("Failed to get all web assets: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[get("/vulnerabilities")]
async fn get_all_vulnerabilities(
    db: web::Data<Database>,
    query: web::Query<AllVulnQuery>,
) -> Result<HttpResponse> {
    match db
        .get_all_vulnerabilities(query.page.unwrap_or(1), query.size.unwrap_or(20))
        .await
    {
        Ok(vulnerabilities) => Ok(HttpResponse::Ok().json(ApiResponse::success(vulnerabilities))),
        Err(e) => {
            log::error!("Failed to get all vulnerabilities: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

#[derive(serde::Deserialize)]
struct PortQuery {
    host_id: Option<i64>,
}

#[derive(serde::Deserialize)]
struct VulnQuery {
    severity: Option<String>,
}

#[derive(serde::Deserialize)]
struct StatisticsQuery {
    task_id: Option<Uuid>,
}

#[derive(serde::Deserialize)]
struct AllVulnQuery {
    page: Option<u32>,
    size: Option<u32>,
    severity: Option<String>,
}

#[derive(serde::Deserialize)]
struct PaginationQuery {
    page: Option<u32>,
    size: Option<u32>,
}
