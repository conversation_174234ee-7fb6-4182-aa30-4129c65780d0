use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Vulnerability {
    pub id: i64,
    pub task_id: Uuid,
    pub host_id: Option<i64>,
    pub web_asset_id: Option<i64>,
    pub vulnerability_id: Option<String>,
    pub severity: String,
    pub title: String,
    pub description: Option<String>,
    pub solution: Option<String>,
    pub reference: Option<String>,
    pub proof_of_concept: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct VulnerabilitySummary {
    pub total: i64,
    pub critical: i64,
    pub high: i64,
    pub medium: i64,
    pub low: i64,
    pub info: i64,
}

#[derive(Debug, Serialize)]
pub struct VulnerabilityDetails {
    pub vulnerability: Vulnerability,
    pub host_info: Option<super::host::Host>,
    pub web_asset_info: Option<WebAsset>,
}

use super::web_asset::WebAsset;
