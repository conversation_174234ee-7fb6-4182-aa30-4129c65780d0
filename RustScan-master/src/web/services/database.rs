use crate::models::host::Host;
use crate::models::port_scan::PortScan;
use crate::models::task::{CreateTaskRequest, ScanTask, TaskStatus};
use crate::models::vulnerability::Vulnerability;
use crate::models::web_asset::WebAsset;
use anyhow::Result;
use chrono::Utc;
use serde::Serialize;
use sqlx::{Row, SqlitePool};
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, Serialize)]
pub struct ScanStatistics {
    pub total_tasks: i64,
    pub total_hosts: i64,
    pub total_ports: i64,
    pub total_vulnerabilities: i64,
    pub total_web_assets: i64,
    pub total_pages: i64,
    pub vulnerability_by_severity: HashMap<String, i64>,
}

#[derive(Clone, Debug)]
pub struct Database {
    pub pool: SqlitePool,
}

impl Database {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    pub async fn create_task(&self, req: CreateTaskRequest) -> Result<ScanTask> {
        let task_id = Uuid::new_v4();
        let now = Utc::now();

        let task = ScanTask {
            id: task_id,
            name: req.name.clone(),
            target: req.target.clone(),
            task_type: req.task_type.clone(),
            status: TaskStatus::Pending,
            config: req.config.clone(),
            created_at: now,
            started_at: None,
            completed_at: None,
            progress: 0,
            total_steps: req.total_steps.unwrap_or(7),
        };

        sqlx::query("INSERT INTO scan_tasks (id, name, target, task_type, status, config, created_at, progress, total_steps) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)")
            .bind(task.id.to_string())
            .bind(&task.name)
            .bind(&task.target)
            .bind(&task.task_type)
            .bind(task.status as i32)
            .bind(&task.config)
            .bind(task.created_at)
            .bind(task.progress)
            .bind(task.total_steps)
            .execute(&self.pool)
            .await?;

        Ok(task)
    }

    pub async fn get_tasks(&self, query: TaskQuery) -> Result<Vec<ScanTask>> {
        let page = query.page.unwrap_or(1);
        let per_page = query.per_page.unwrap_or(10);
        let offset = (page - 1) * per_page;

        let mut sql = "SELECT * FROM scan_tasks".to_string();
        let mut bindings = Vec::new();

        if query.status.is_some() {
            sql.push_str(" WHERE status = ?");
            bindings.push(query.status.unwrap() as i32);
        }

        sql.push_str(" ORDER BY created_at DESC LIMIT ? OFFSET ?");

        let mut q = sqlx::query(&sql);
        for binding in bindings {
            q = q.bind(binding);
        }
        q = q.bind(per_page as i64).bind(offset as i64);

        let rows = q.fetch_all(&self.pool).await?;

        let mut tasks = Vec::new();
        for row in rows {
            let id_str: String = row.get("id");
            tasks.push(ScanTask {
                id: Uuid::parse_str(&id_str)?,
                name: row.get("name"),
                target: row.get("target"),
                task_type: row.get("task_type"),
                status: TaskStatus::from_i32(row.get("status")),
                config: row.get("config"),
                created_at: row.get("created_at"),
                started_at: row.get("started_at"),
                completed_at: row.get("completed_at"),
                progress: row.get("progress"),
                total_steps: row.get("total_steps"),
            });
        }

        Ok(tasks)
    }

    pub async fn get_task(&self, task_id: Uuid) -> Result<Option<ScanTask>> {
        let row = sqlx::query("SELECT * FROM scan_tasks WHERE id = ?")
            .bind(task_id.to_string())
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = row {
            let id_str: String = row.get("id");
            Ok(Some(ScanTask {
                id: Uuid::parse_str(&id_str)?,
                name: row.get("name"),
                target: row.get("target"),
                task_type: row.get("task_type"),
                status: TaskStatus::from_i32(row.get("status")),
                config: row.get("config"),
                created_at: row.get("created_at"),
                started_at: row.get("started_at"),
                completed_at: row.get("completed_at"),
                progress: row.get("progress"),
                total_steps: row.get("total_steps"),
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn update_task_status(&self, task_id: Uuid, status: TaskStatus) -> Result<()> {
        match status {
            TaskStatus::Running => {
                sqlx::query("UPDATE scan_tasks SET status = ?, started_at = ? WHERE id = ?")
                    .bind(status as i32)
                    .bind(Utc::now())
                    .bind(task_id.to_string())
                    .execute(&self.pool)
                    .await?;
            }
            TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Stopped => {
                sqlx::query("UPDATE scan_tasks SET status = ?, completed_at = ? WHERE id = ?")
                    .bind(status as i32)
                    .bind(Utc::now())
                    .bind(task_id.to_string())
                    .execute(&self.pool)
                    .await?;
            }
            _ => {
                sqlx::query("UPDATE scan_tasks SET status = ? WHERE id = ?")
                    .bind(status as i32)
                    .bind(task_id.to_string())
                    .execute(&self.pool)
                    .await?;
            }
        }

        Ok(())
    }

    pub async fn delete_task(&self, task_id: Uuid) -> Result<()> {
        sqlx::query("DELETE FROM scan_tasks WHERE id = ?")
            .bind(task_id.to_string())
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn update_task_progress(&self, task_id: Uuid, progress: i32) -> Result<()> {
        sqlx::query("UPDATE scan_tasks SET progress = ? WHERE id = ?")
            .bind(progress)
            .bind(task_id.to_string())
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    // 主机相关操作
    pub async fn get_hosts_by_task(&self, task_id: Uuid) -> Result<Vec<Host>> {
        let rows = sqlx::query("SELECT * FROM hosts WHERE task_id = ? ORDER BY ip_address")
            .bind(task_id.to_string())
            .fetch_all(&self.pool)
            .await?;

        let mut hosts = Vec::new();
        for row in rows {
            let task_id_str: String = row.get("task_id");
            hosts.push(Host {
                id: row.get("id"),
                task_id: Uuid::parse_str(&task_id_str)?,
                ip_address: row.get("ip_address"),
                hostname: row.get("hostname"),
                os_info: row.get("os_info"),
                status: row.get("status"),
                created_at: row.get("created_at"),
            });
        }

        Ok(hosts)
    }

    // 端口扫描相关操作
    pub async fn get_ports_by_task(
        &self,
        task_id: Uuid,
        host_id: Option<i64>,
    ) -> Result<Vec<PortScan>> {
        let rows = if let Some(host_id) = host_id {
            sqlx::query("SELECT * FROM port_scans WHERE task_id = ? AND host_id = ? ORDER BY port")
                .bind(task_id.to_string())
                .bind(host_id)
                .fetch_all(&self.pool)
                .await?
        } else {
            sqlx::query("SELECT * FROM port_scans WHERE task_id = ? ORDER BY port")
                .bind(task_id.to_string())
                .fetch_all(&self.pool)
                .await?
        };

        let mut ports = Vec::new();
        for row in rows {
            let task_id_str: String = row.get("task_id");
            ports.push(PortScan {
                id: row.get("id"),
                task_id: Uuid::parse_str(&task_id_str)?,
                host_id: row.get("host_id"),
                port: row.get("port"),
                protocol: row.get("protocol"),
                status: row.get("status"),
                service: row.get("service"),
                version: row.get("version"),
                banner: row.get("banner"),
                created_at: row.get("created_at"),
            });
        }

        Ok(ports)
    }

    // 漏洞相关操作
    pub async fn get_vulnerabilities_by_task(
        &self,
        task_id: Uuid,
        severity: Option<String>,
    ) -> Result<Vec<Vulnerability>> {
        let rows = if let Some(severity) = severity {
            sqlx::query("SELECT * FROM vulnerabilities WHERE task_id = ? AND severity = ? ORDER BY created_at DESC")
                .bind(task_id.to_string())
                .bind(severity)
                .fetch_all(&self.pool)
                .await?
        } else {
            sqlx::query("SELECT * FROM vulnerabilities WHERE task_id = ? ORDER BY created_at DESC")
                .bind(task_id.to_string())
                .fetch_all(&self.pool)
                .await?
        };

        let mut vulnerabilities = Vec::new();
        for row in rows {
            let task_id_str: String = row.get("task_id");
            vulnerabilities.push(Vulnerability {
                id: row.get("id"),
                task_id: Uuid::parse_str(&task_id_str)?,
                host_id: row.get("host_id"),
                web_asset_id: row.get("web_asset_id"),
                vulnerability_id: row.get("vulnerability_id"),
                severity: row.get("severity"),
                title: row.get("title"),
                description: row.get("description"),
                solution: row.get("solution"),
                reference: row.get("reference"),
                proof_of_concept: row.get("proof_of_concept"),
                created_at: row.get("created_at"),
            });
        }

        Ok(vulnerabilities)
    }

    // 日志相关操作
    pub async fn add_task_log(&self, task_id: Uuid, level: &str, message: &str) -> Result<()> {
        sqlx::query("INSERT INTO task_logs (task_id, level, message) VALUES (?, ?, ?)")
            .bind(task_id.to_string())
            .bind(level)
            .bind(message)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn get_scan_statistics(&self, task_id: Option<Uuid>) -> Result<ScanStatistics> {
        let mut stats = ScanStatistics {
            total_tasks: 0,
            total_hosts: 0,
            total_ports: 0,
            total_vulnerabilities: 0,
            total_web_assets: 0,
            total_pages: 0,
            vulnerability_by_severity: std::collections::HashMap::new(),
        };

        // 获取任务统计
        let task_count = if let Some(task_id) = task_id {
            sqlx::query_scalar("SELECT COUNT(*) FROM scan_tasks WHERE id = ?")
                .bind(task_id.to_string())
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar("SELECT COUNT(*) FROM scan_tasks")
                .fetch_one(&self.pool)
                .await?
        };
        stats.total_tasks = task_count;

        // 获取主机统计
        let host_count = if let Some(task_id) = task_id {
            sqlx::query_scalar("SELECT COUNT(*) FROM hosts WHERE task_id = ?")
                .bind(task_id.to_string())
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar("SELECT COUNT(*) FROM hosts")
                .fetch_one(&self.pool)
                .await?
        };
        stats.total_hosts = host_count;

        // 获取端口统计
        let port_count = if let Some(task_id) = task_id {
            sqlx::query_scalar("SELECT COUNT(*) FROM port_scans WHERE task_id = ?")
                .bind(task_id.to_string())
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar("SELECT COUNT(*) FROM port_scans")
                .fetch_one(&self.pool)
                .await?
        };
        stats.total_ports = port_count;

        // 获取漏洞统计
        let vuln_count = if let Some(task_id) = task_id {
            sqlx::query_scalar("SELECT COUNT(*) FROM vulnerabilities WHERE task_id = ?")
                .bind(task_id.to_string())
                .fetch_one(&self.pool)
                .await?
        } else {
            sqlx::query_scalar("SELECT COUNT(*) FROM vulnerabilities")
                .fetch_one(&self.pool)
                .await?
        };
        stats.total_vulnerabilities = vuln_count;

        Ok(stats)
    }

    pub async fn get_all_vulnerabilities(
        &self,
        page: u32,
        size: u32,
    ) -> Result<Vec<Vulnerability>> {
        let offset = (page - 1) * size;

        let rows = sqlx::query(
            "SELECT id, task_id, host_id, web_asset_id, vulnerability_id, severity, title, description, solution, reference, proof_of_concept, created_at FROM vulnerabilities ORDER BY created_at DESC LIMIT ? OFFSET ?",
        )
        .bind(size as i64)
        .bind(offset as i64)
        .fetch_all(&self.pool)
        .await?;

        let mut vulnerabilities = Vec::new();
        for row in rows {
            let task_id_str: String = row.get("task_id");
            let task_id = Uuid::parse_str(&task_id_str)?;

            vulnerabilities.push(Vulnerability {
                id: row.get("id"),
                task_id,
                host_id: row.get("host_id"),
                web_asset_id: row.get("web_asset_id"),
                vulnerability_id: row.get("vulnerability_id"),
                severity: row.get("severity"),
                title: row.get("title"),
                description: row.get("description"),
                solution: row.get("solution"),
                reference: row.get("reference"),
                proof_of_concept: row.get("proof_of_concept"),
                created_at: row.get("created_at"),
            });
        }

        Ok(vulnerabilities)
    }

    pub async fn get_all_hosts(&self, page: u32, size: u32) -> Result<Vec<Host>> {
        let offset = (page - 1) * size;

        let rows = sqlx::query(
            "SELECT id, task_id, ip_address, hostname, os_info, status, created_at FROM hosts ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(size as i64)
        .bind(offset as i64)
        .fetch_all(&self.pool)
        .await?;

        let mut hosts = Vec::new();
        for row in rows {
            let task_id_str: String = row.get("task_id");
            let task_id = Uuid::parse_str(&task_id_str)?;

            hosts.push(Host {
                id: row.get("id"),
                task_id,
                ip_address: row.get("ip_address"),
                hostname: row.get("hostname"),
                os_info: row.get("os_info"),
                status: row
                    .get::<Option<String>, _>("status")
                    .unwrap_or_else(|| "unknown".to_string()),
                created_at: row.get("created_at"),
            });
        }

        Ok(hosts)
    }

    pub async fn get_all_ports(&self, page: u32, size: u32) -> Result<Vec<PortScan>> {
        let offset = (page - 1) * size;

        let rows = sqlx::query(
            "SELECT id, task_id, host_id, port, protocol, status, service, version, banner, created_at FROM port_scans ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(size as i64)
        .bind(offset as i64)
        .fetch_all(&self.pool)
        .await?;

        let mut ports = Vec::new();
        for row in rows {
            let task_id_str: String = row.get("task_id");
            let task_id = Uuid::parse_str(&task_id_str)?;

            ports.push(PortScan {
                id: row.get("id"),
                task_id,
                host_id: row.get("host_id"),
                port: row.get("port"),
                protocol: row.get("protocol"),
                status: row
                    .get::<Option<String>, _>("status")
                    .unwrap_or_else(|| "unknown".to_string()),
                service: row.get("service"),
                version: row.get("version"),
                banner: row.get("banner"),
                created_at: row.get("created_at"),
            });
        }

        Ok(ports)
    }

    pub async fn get_all_web_assets(&self, page: u32, size: u32) -> Result<Vec<WebAsset>> {
        let offset = (page - 1) * size;

        // 由于web_assets表可能不存在，我们返回空列表
        // 在实际实现中，你需要根据数据库schema调整
        Ok(Vec::new())
    }
}

#[derive(Debug)]
pub struct TaskQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub status: Option<TaskStatus>,
}
